<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share Your Story</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(139, 69, 19, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 100px 20px 60px;
        }

        .hero-content {
            max-width: 600px;
            animation: fadeInUp 1s ease-out;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        .hero-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
        }

        .hero-btn.secondary {
            background: transparent;
            color: #8B4513;
            border: 2px solid #8B4513;
            box-shadow: none;
        }

        .hero-btn.secondary:hover {
            background: #8B4513;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 15px;
            }

            .hero {
                padding: 80px 15px 40px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Authentication Modal */
        .auth-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 400px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .auth-modal-header {
            padding: 30px 30px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .auth-modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #666;
        }

        .auth-modal-body {
            padding: 20px 30px 30px;
        }

        .auth-form {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #8B4513;
            background: white;
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .auth-btn {
            width: 100%;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
        }

        .auth-btn:active {
            transform: translateY(0);
        }

        .auth-switch {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        .auth-switch a {
            color: #8B4513;
            text-decoration: none;
            font-weight: 600;
        }

        .auth-switch a:hover {
            text-decoration: underline;
        }

        .auth-error {
            background: #fee;
            color: #c33;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #fcc;
        }

        .auth-loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f0f0f0;
            border-top: 3px solid #8B4513;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Mobile responsiveness for modal */
        @media (max-width: 768px) {
            .auth-modal {
                padding: 10px;
            }

            .auth-modal-content {
                max-width: 100%;
                border-radius: 15px;
            }

            .auth-modal-header {
                padding: 20px 20px 15px;
            }

            .auth-modal-header h3 {
                font-size: 20px;
            }

            .auth-modal-body {
                padding: 15px 20px 20px;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Naroop</div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Connect & Share</h1>
            <p class="hero-subtitle">A place where our community comes together to share positive experiences, inspire each other, and build meaningful connections.</p>
            <div class="hero-buttons">
                <button class="hero-btn">Join Our Community</button>
                <button class="hero-btn secondary">Sign In</button>
            </div>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="authModal" class="auth-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h3 id="authModalTitle">Welcome to Naroop</h3>
                <button id="closeAuthModal" class="close-btn">&times;</button>
            </div>
            <div class="auth-modal-body">
                <!-- Sign In Form -->
                <div id="signInForm" class="auth-form">
                    <div class="form-group">
                        <label for="signInEmail">Email</label>
                        <input type="email" id="signInEmail" placeholder="Enter your email" required>
                    </div>
                    <div class="form-group">
                        <label for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" placeholder="Enter your password" required>
                    </div>
                    <button id="signInBtn" class="auth-btn">Sign In</button>
                    <p class="auth-switch">Don't have an account? <a href="#" id="showSignUpForm">Join our community</a></p>
                </div>

                <!-- Sign Up Form -->
                <div id="signUpForm" class="auth-form" style="display: none;">
                    <div class="form-group">
                        <label for="signUpUsername">Username</label>
                        <input type="text" id="signUpUsername" placeholder="Choose a username" required>
                    </div>
                    <div class="form-group">
                        <label for="signUpEmail">Email</label>
                        <input type="email" id="signUpEmail" placeholder="Enter your email" required>
                    </div>
                    <div class="form-group">
                        <label for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" placeholder="Create a password" required>
                    </div>
                    <button id="signUpBtn" class="auth-btn">Join Our Community</button>
                    <p class="auth-switch">Already have an account? <a href="#" id="showSignInForm">Sign in</a></p>
                </div>

                <div id="authError" class="auth-error" style="display: none;"></div>
                <div id="authLoading" class="auth-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Please wait...</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Import Firebase authentication
        import { FirebaseAuth } from './public/js/firebase-config.js';

        // Initialize Firebase when page loads
        let firebaseInitialized = false;

        document.addEventListener('DOMContentLoaded', async function() {
            // Check if user is already authenticated
            const currentUser = localStorage.getItem('currentUser');
            const authToken = localStorage.getItem('authToken');

            if (currentUser && authToken) {
                // User is already logged in, redirect to main page
                window.location.href = '/index.html';
                return;
            }

            // Initialize Firebase
            try {
                firebaseInitialized = await FirebaseAuth.init();
                if (firebaseInitialized) {
                    console.log('Firebase initialized successfully');

                    // Listen for auth state changes
                    FirebaseAuth.onAuthStateChanged((user) => {
                        if (user) {
                            // User is signed in, redirect to main page
                            localStorage.setItem('currentUser', JSON.stringify(user));
                            window.location.href = '/index.html';
                        }
                    });
                } else {
                    console.warn('Firebase initialization failed, using fallback authentication');
                }
            } catch (error) {
                console.error('Error initializing Firebase:', error);
            }
        });

        // Smooth scroll for logo click
        document.querySelector('.logo').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Modal functionality
        const authModal = document.getElementById('authModal');
        const closeAuthModal = document.getElementById('closeAuthModal');
        const signInForm = document.getElementById('signInForm');
        const signUpForm = document.getElementById('signUpForm');
        const showSignUpForm = document.getElementById('showSignUpForm');
        const showSignInForm = document.getElementById('showSignInForm');
        const authModalTitle = document.getElementById('authModalTitle');
        const authError = document.getElementById('authError');
        const authLoading = document.getElementById('authLoading');

        // Button click handlers
        document.querySelectorAll('.hero-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === 'Join Our Community') {
                    openModal('signup');
                } else if (this.textContent === 'Sign In') {
                    openModal('signin');
                }
            });
        });

        function openModal(mode) {
            authModal.style.display = 'flex';
            hideError();
            hideLoading();

            if (mode === 'signup') {
                showSignUpFormView();
            } else {
                showSignInFormView();
            }
        }

        function closeModal() {
            authModal.style.display = 'none';
            clearForms();
        }

        function showSignInFormView() {
            signInForm.style.display = 'block';
            signUpForm.style.display = 'none';
            authModalTitle.textContent = 'Welcome Back';
        }

        function showSignUpFormView() {
            signInForm.style.display = 'none';
            signUpForm.style.display = 'block';
            authModalTitle.textContent = 'Join Our Community';
        }

        function showError(message) {
            authError.textContent = message;
            authError.style.display = 'block';
        }

        function hideError() {
            authError.style.display = 'none';
        }

        function showLoading() {
            authLoading.style.display = 'block';
        }

        function hideLoading() {
            authLoading.style.display = 'none';
        }

        function clearForms() {
            document.getElementById('signInEmail').value = '';
            document.getElementById('signInPassword').value = '';
            document.getElementById('signUpUsername').value = '';
            document.getElementById('signUpEmail').value = '';
            document.getElementById('signUpPassword').value = '';
            hideError();
            hideLoading();
        }

        // Event listeners
        closeAuthModal.addEventListener('click', closeModal);
        showSignUpForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignUpFormView();
        });
        showSignInForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignInFormView();
        });

        // Close modal when clicking outside
        authModal.addEventListener('click', (e) => {
            if (e.target === authModal) {
                closeModal();
            }
        });

        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', async function() {
            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            if (!email || !password) {
                showError('Please fill in all fields');
                return;
            }

            showLoading();
            hideError();

            try {
                if (firebaseInitialized) {
                    // Use Firebase authentication
                    const result = await FirebaseAuth.signIn(email, password);

                    if (result.success) {
                        // Store user data and redirect
                        localStorage.setItem('currentUser', JSON.stringify(result.user));

                        // Get Firebase ID token for server authentication
                        const idToken = await FirebaseAuth.getIdToken();
                        if (idToken) {
                            localStorage.setItem('authToken', idToken);
                        }

                        // Sync with server
                        await syncUserWithServer(result.user, idToken);

                        window.location.href = '/index.html';
                    } else {
                        showError(result.error || 'Sign in failed');
                    }
                } else {
                    // Fallback to server authentication
                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email, password })
                    });

                    const data = await response.json();

                    if (data.success) {
                        localStorage.setItem('currentUser', JSON.stringify(data.user));
                        window.location.href = '/index.html';
                    } else {
                        showError(data.error || 'Sign in failed');
                    }
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });

        // Sign Up functionality
        document.getElementById('signUpBtn').addEventListener('click', async function() {
            const username = document.getElementById('signUpUsername').value.trim();
            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;

            if (!username || !email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long');
                return;
            }

            showLoading();
            hideError();

            try {
                if (firebaseInitialized) {
                    // Use Firebase authentication
                    const result = await FirebaseAuth.signUp(email, password, username);

                    if (result.success) {
                        // Store user data and redirect
                        localStorage.setItem('currentUser', JSON.stringify(result.user));

                        // Get Firebase ID token for server authentication
                        const idToken = await FirebaseAuth.getIdToken();
                        if (idToken) {
                            localStorage.setItem('authToken', idToken);
                        }

                        // Sync with server
                        await syncUserWithServer(result.user, idToken);

                        window.location.href = '/index.html';
                    } else {
                        showError(result.error || 'Sign up failed');
                    }
                } else {
                    // Fallback to server authentication
                    const response = await fetch('/api/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, email, password })
                    });

                    const data = await response.json();

                    if (data.success) {
                        localStorage.setItem('currentUser', JSON.stringify(data.user));
                        window.location.href = '/index.html';
                    } else {
                        showError(data.error || 'Sign up failed');
                    }
                }
            } catch (error) {
                console.error('Sign up error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });

        // Sync user with server
        async function syncUserWithServer(user, idToken) {
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(idToken && { 'Authorization': `Bearer ${idToken}` })
                    },
                    body: JSON.stringify({
                        uid: user.uid,
                        username: user.username,
                        email: user.email
                    })
                });

                if (!response.ok) {
                    console.warn('Failed to sync user with server');
                }
            } catch (error) {
                console.error('Error syncing user with server:', error);
            }
        }
    </script>
</body>
</html>